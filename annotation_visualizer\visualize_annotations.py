#!/usr/bin/env python3
"""
YOLO标注可视化工具
用于可视化output-dataset中的图片和对应的标注文件
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import argparse
import random
from typing import List, Tuple, Dict

# 设置matplotlib支持中文
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
plt.rcParams['axes.unicode_minus'] = False

class AnnotationVisualizer:
    def __init__(self, dataset_path: str, output_path: str = "annotation_visualizer/visualized_results"):
        """
        初始化标注可视化器
        
        Args:
            dataset_path: 数据集路径
            output_path: 输出结果路径
        """
        self.dataset_path = Path(dataset_path)
        self.output_path = Path(output_path)
        self.image_path = self.dataset_path / "picture"
        self.annotation_path = self.dataset_path
        
        # 创建输出目录
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        # 读取类别信息
        self.classes = self._load_classes()
        
        # 定义颜色映射（为每个类别分配不同颜色）
        self.colors = self._generate_colors(len(self.classes))
        
    def _load_classes(self) -> List[str]:
        """加载类别文件"""
        classes_file = self.dataset_path / "classes.txt"
        if classes_file.exists():
            with open(classes_file, 'r', encoding='utf-8') as f:
                classes = [line.strip() for line in f.readlines() if line.strip()]
            return classes
        else:
            return ["pig"]  # 默认类别
    
    def _generate_colors(self, num_classes: int) -> List[Tuple[int, int, int]]:
        """为每个类别生成不同的颜色"""
        colors = []
        for i in range(num_classes):
            # 使用HSV色彩空间生成均匀分布的颜色
            hue = i * 360 / num_classes
            # 转换为RGB
            import colorsys
            rgb = colorsys.hsv_to_rgb(hue/360, 0.8, 0.9)
            colors.append(tuple(int(c * 255) for c in rgb))
        return colors
    
    def _parse_annotation(self, annotation_file: Path) -> List[Dict]:
        """解析YOLO格式的标注文件"""
        annotations = []
        if annotation_file.exists():
            with open(annotation_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line:
                        parts = line.split()
                        if len(parts) >= 5:
                            class_id = int(parts[0])
                            x_center = float(parts[1])
                            y_center = float(parts[2])
                            width = float(parts[3])
                            height = float(parts[4])
                            
                            annotations.append({
                                'class_id': class_id,
                                'x_center': x_center,
                                'y_center': y_center,
                                'width': width,
                                'height': height
                            })
        return annotations
    
    def _convert_yolo_to_bbox(self, annotation: Dict, img_width: int, img_height: int) -> Tuple[int, int, int, int]:
        """将YOLO格式坐标转换为边界框坐标"""
        x_center = annotation['x_center'] * img_width
        y_center = annotation['y_center'] * img_height
        width = annotation['width'] * img_width
        height = annotation['height'] * img_height
        
        x1 = int(x_center - width / 2)
        y1 = int(y_center - height / 2)
        x2 = int(x_center + width / 2)
        y2 = int(y_center + height / 2)
        
        return x1, y1, x2, y2
    
    def visualize_single_image(self, image_name: str, save_result: bool = True) -> bool:
        """
        可视化单张图片的标注
        
        Args:
            image_name: 图片名称（不含扩展名）
            save_result: 是否保存结果
            
        Returns:
            bool: 是否成功处理
        """
        # 构建文件路径
        image_file = self.image_path / f"{image_name}.jpg"
        annotation_file = self.annotation_path / f"{image_name}.txt"
        
        if not image_file.exists():
            print(f"图片文件不存在: {image_file}")
            return False
        
        # 读取图片
        image = cv2.imread(str(image_file))
        if image is None:
            print(f"无法读取图片: {image_file}")
            return False
        
        # 转换颜色空间（OpenCV使用BGR，matplotlib使用RGB）
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        img_height, img_width = image_rgb.shape[:2]
        
        # 解析标注
        annotations = self._parse_annotation(annotation_file)
        
        # 创建图形
        fig, ax = plt.subplots(1, 1, figsize=(12, 8))
        ax.imshow(image_rgb)
        
        # 绘制边界框
        for annotation in annotations:
            class_id = annotation['class_id']
            x1, y1, x2, y2 = self._convert_yolo_to_bbox(annotation, img_width, img_height)
            
            # 获取类别名称和颜色
            class_name = self.classes[class_id] if class_id < len(self.classes) else f"class_{class_id}"
            color = self.colors[class_id % len(self.colors)]
            color_normalized = tuple(c/255.0 for c in color)
            
            # 绘制边界框
            rect = patches.Rectangle(
                (x1, y1), x2-x1, y2-y1,
                linewidth=2, edgecolor=color_normalized, facecolor='none'
            )
            ax.add_patch(rect)
            
            # 添加标签
            ax.text(x1, y1-5, f"{class_name}", 
                   bbox=dict(boxstyle="round,pad=0.3", facecolor=color_normalized, alpha=0.7),
                   fontsize=10, color='white', weight='bold')
        
        # 设置标题和去除坐标轴
        ax.set_title(f"标注可视化: {image_name}", fontsize=14, weight='bold')
        ax.axis('off')
        
        # 保存结果
        if save_result:
            output_file = self.output_path / f"{image_name}_annotated.png"
            plt.savefig(output_file, dpi=150, bbox_inches='tight', pad_inches=0.1)
            print(f"已保存可视化结果: {output_file}")
        
        plt.tight_layout()
        plt.show()
        plt.close()
        
        return True
    
    def visualize_random_samples(self, num_samples: int = 10, save_results: bool = True):
        """随机可视化多个样本"""
        # 获取所有图片文件
        image_files = list(self.image_path.glob("*.jpg"))
        if not image_files:
            print("未找到图片文件")
            return
        
        # 随机选择样本
        selected_files = random.sample(image_files, min(num_samples, len(image_files)))
        
        print(f"正在可视化 {len(selected_files)} 个随机样本...")
        
        for i, image_file in enumerate(selected_files):
            image_name = image_file.stem
            print(f"处理 {i+1}/{len(selected_files)}: {image_name}")
            self.visualize_single_image(image_name, save_results)
    
    def create_summary_report(self):
        """创建数据集摘要报告"""
        # 统计信息
        image_files = list(self.image_path.glob("*.jpg"))
        annotation_files = list(self.annotation_path.glob("*.txt"))
        annotation_files = [f for f in annotation_files if f.name != "classes.txt"]
        
        total_annotations = 0
        class_counts = {i: 0 for i in range(len(self.classes))}
        
        for ann_file in annotation_files:
            annotations = self._parse_annotation(ann_file)
            total_annotations += len(annotations)
            for ann in annotations:
                class_id = ann['class_id']
                if class_id in class_counts:
                    class_counts[class_id] += 1
        
        # 创建报告
        report = f"""
数据集标注质量报告
==================

数据集路径: {self.dataset_path}
输出路径: {self.output_path}

基本统计:
- 图片总数: {len(image_files)}
- 标注文件总数: {len(annotation_files)}
- 标注框总数: {total_annotations}
- 平均每张图片标注框数: {total_annotations/len(image_files):.2f}

类别统计:
"""
        for class_id, count in class_counts.items():
            class_name = self.classes[class_id] if class_id < len(self.classes) else f"class_{class_id}"
            report += f"- {class_name}: {count} 个标注框\n"
        
        # 保存报告
        report_file = self.output_path / "dataset_summary.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(report)
        print(f"报告已保存到: {report_file}")

def main():
    parser = argparse.ArgumentParser(description="YOLO标注可视化工具")
    parser.add_argument("--dataset", default="output-dataset", help="数据集路径")
    parser.add_argument("--output", default="annotation_visualizer/visualized_results", help="输出路径")
    parser.add_argument("--samples", type=int, default=10, help="随机可视化样本数量")
    parser.add_argument("--image", type=str, help="指定可视化的图片名称（不含扩展名）")
    
    args = parser.parse_args()
    
    # 创建可视化器
    visualizer = AnnotationVisualizer(args.dataset, args.output)
    
    # 创建摘要报告
    visualizer.create_summary_report()
    
    if args.image:
        # 可视化指定图片
        visualizer.visualize_single_image(args.image)
    else:
        # 随机可视化样本
        visualizer.visualize_random_samples(args.samples)

if __name__ == "__main__":
    main()
