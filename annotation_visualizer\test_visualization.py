#!/usr/bin/env python3
"""
测试脚本 - 直接运行可视化功能
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from visualize_annotations import AnnotationVisualizer

def main():
    print("=== 测试YOLO标注可视化工具 ===")
    
    # 数据集路径
    dataset_path = "../output-dataset"
    output_path = "visualized_results"
    
    # 检查数据集是否存在
    if not Path(dataset_path).exists():
        print(f"错误: 数据集路径不存在: {dataset_path}")
        return
    
    try:
        # 创建可视化器
        print(f"初始化可视化器...")
        print(f"数据集路径: {dataset_path}")
        print(f"输出路径: {output_path}")
        
        visualizer = AnnotationVisualizer(dataset_path, output_path)
        
        # 生成数据集摘要报告
        print("\n1. 生成数据集摘要报告...")
        visualizer.create_summary_report()
        
        # 可视化几个指定的样本（不显示图片，只保存）
        print("\n2. 测试可视化指定样本...")
        test_samples = ["output_001", "output_002", "output_003"]
        
        for sample in test_samples:
            print(f"处理样本: {sample}")
            success = visualizer.visualize_single_image(sample, save_result=True)
            if success:
                print(f"  [OK] 成功处理 {sample}")
            else:
                print(f"  [FAIL] 处理失败 {sample}")
        
        # 随机可视化3个样本
        print("\n3. 随机可视化3个样本...")
        visualizer.visualize_random_samples(3, save_results=True)
        
        print("\n=== 测试完成 ===")
        print(f"请查看输出文件夹: {output_path}")
        print("所有可视化结果已保存为PNG文件")
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
