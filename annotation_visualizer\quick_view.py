#!/usr/bin/env python3
"""
快速查看标注效果的简化脚本
"""

import cv2
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from pathlib import Path
import random

# 设置matplotlib支持中文（如果可能的话）
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False
except:
    pass

def load_annotation(annotation_file):
    """加载YOLO格式的标注文件"""
    annotations = []
    if annotation_file.exists():
        with open(annotation_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    parts = line.split()
                    if len(parts) >= 5:
                        class_id = int(parts[0])
                        x_center = float(parts[1])
                        y_center = float(parts[2])
                        width = float(parts[3])
                        height = float(parts[4])
                        annotations.append((class_id, x_center, y_center, width, height))
    return annotations

def convert_yolo_to_bbox(annotation, img_width, img_height):
    """将YOLO格式坐标转换为边界框坐标"""
    class_id, x_center, y_center, width, height = annotation
    
    x_center *= img_width
    y_center *= img_height
    width *= img_width
    height *= img_height
    
    x1 = int(x_center - width / 2)
    y1 = int(y_center - height / 2)
    x2 = int(x_center + width / 2)
    y2 = int(y_center + height / 2)
    
    return x1, y1, x2, y2

def visualize_image(image_path, annotation_path, output_path=None):
    """可视化单张图片的标注"""
    # 读取图片
    image = cv2.imread(str(image_path))
    if image is None:
        print(f"无法读取图片: {image_path}")
        return False
    
    # 转换颜色空间
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    img_height, img_width = image_rgb.shape[:2]
    
    # 加载标注
    annotations = load_annotation(annotation_path)
    
    # 创建图形
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))
    ax.imshow(image_rgb)
    
    # 绘制边界框
    colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'cyan', 'magenta']
    
    for i, annotation in enumerate(annotations):
        class_id = annotation[0]
        x1, y1, x2, y2 = convert_yolo_to_bbox(annotation, img_width, img_height)
        
        # 选择颜色
        color = colors[class_id % len(colors)]
        
        # 绘制边界框
        rect = patches.Rectangle(
            (x1, y1), x2-x1, y2-y1,
            linewidth=2, edgecolor=color, facecolor='none'
        )
        ax.add_patch(rect)
        
        # 添加标签
        ax.text(x1, y1-5, f"pig_{i+1}", 
               bbox=dict(boxstyle="round,pad=0.3", facecolor=color, alpha=0.7),
               fontsize=10, color='white', weight='bold')
    
    # 设置标题
    ax.set_title(f"Annotation Visualization: {image_path.name} ({len(annotations)} objects)", 
                fontsize=14, weight='bold')
    ax.axis('off')
    
    # 保存结果
    if output_path:
        plt.savefig(output_path, dpi=150, bbox_inches='tight', pad_inches=0.1)
        print(f"Saved: {output_path}")
    
    plt.tight_layout()
    plt.show()
    plt.close()
    
    return True

def main():
    """主函数"""
    print("=== Quick Annotation Viewer ===")
    
    # 设置路径
    dataset_path = Path("../output-dataset")
    image_dir = dataset_path / "picture"
    output_dir = Path("visualized_results")
    output_dir.mkdir(exist_ok=True)
    
    if not dataset_path.exists():
        print(f"Dataset not found: {dataset_path}")
        return
    
    # 获取所有图片文件
    image_files = list(image_dir.glob("*.jpg"))
    if not image_files:
        print("No image files found")
        return
    
    print(f"Found {len(image_files)} images")
    
    # 随机选择5个样本进行可视化
    num_samples = min(5, len(image_files))
    selected_files = random.sample(image_files, num_samples)
    
    print(f"Visualizing {num_samples} random samples...")
    
    for i, image_file in enumerate(selected_files):
        print(f"Processing {i+1}/{num_samples}: {image_file.name}")
        
        # 对应的标注文件
        annotation_file = dataset_path / f"{image_file.stem}.txt"
        
        # 输出文件
        output_file = output_dir / f"{image_file.stem}_annotated.png"
        
        # 可视化
        success = visualize_image(image_file, annotation_file, output_file)
        if not success:
            print(f"Failed to process: {image_file.name}")
    
    print("Done! Check the 'visualized_results' folder for output images.")

if __name__ == "__main__":
    main()
