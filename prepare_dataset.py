#!/usr/bin/env python3
"""
数据集处理脚本
将output-dataset处理成YOLO训练格式，按80%训练集，20%验证集分割
"""

import os
import shutil
import random
from pathlib import Path

def create_dataset_structure(dataset_root):
    """创建YOLO数据集目录结构"""
    dirs = [
        dataset_root / "images" / "train",
        dataset_root / "images" / "val", 
        dataset_root / "labels" / "train",
        dataset_root / "labels" / "val"
    ]
    
    for dir_path in dirs:
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"创建目录: {dir_path}")

def split_dataset(source_dir, target_dir, train_ratio=0.8):
    """
    分割数据集为训练集和验证集
    
    Args:
        source_dir: 源数据集目录 (output-dataset)
        target_dir: 目标数据集目录 (pig_dataset)
        train_ratio: 训练集比例，默认0.8 (80%)
    """
    source_path = Path(source_dir)
    target_path = Path(target_dir)
    
    # 获取所有图片文件
    image_dir = source_path / "picture"
    image_files = list(image_dir.glob("*.jpg"))
    
    if not image_files:
        print(f"错误: 在 {image_dir} 中未找到图片文件")
        return False
    
    print(f"找到 {len(image_files)} 张图片")
    
    # 随机打乱文件列表
    random.shuffle(image_files)
    
    # 计算分割点
    train_count = int(len(image_files) * train_ratio)
    train_files = image_files[:train_count]
    val_files = image_files[train_count:]
    
    print(f"训练集: {len(train_files)} 张图片")
    print(f"验证集: {len(val_files)} 张图片")
    
    # 创建目录结构
    create_dataset_structure(target_path)
    
    # 复制训练集
    print("\n复制训练集文件...")
    for img_file in train_files:
        # 复制图片
        src_img = img_file
        dst_img = target_path / "images" / "train" / img_file.name
        shutil.copy2(src_img, dst_img)
        
        # 复制对应的标注文件
        label_name = img_file.stem + ".txt"
        src_label = source_path / label_name
        dst_label = target_path / "labels" / "train" / label_name
        
        if src_label.exists():
            shutil.copy2(src_label, dst_label)
        else:
            print(f"警告: 标注文件不存在 {src_label}")
    
    # 复制验证集
    print("复制验证集文件...")
    for img_file in val_files:
        # 复制图片
        src_img = img_file
        dst_img = target_path / "images" / "val" / img_file.name
        shutil.copy2(src_img, dst_img)
        
        # 复制对应的标注文件
        label_name = img_file.stem + ".txt"
        src_label = source_path / label_name
        dst_label = target_path / "labels" / "val" / label_name
        
        if src_label.exists():
            shutil.copy2(src_label, dst_label)
        else:
            print(f"警告: 标注文件不存在 {src_label}")
    
    return True

def verify_dataset(dataset_dir):
    """验证数据集完整性"""
    dataset_path = Path(dataset_dir)
    
    # 检查目录结构
    required_dirs = [
        "images/train", "images/val",
        "labels/train", "labels/val"
    ]
    
    for dir_name in required_dirs:
        dir_path = dataset_path / dir_name
        if not dir_path.exists():
            print(f"错误: 目录不存在 {dir_path}")
            return False
    
    # 统计文件数量
    train_images = len(list((dataset_path / "images" / "train").glob("*.jpg")))
    val_images = len(list((dataset_path / "images" / "val").glob("*.jpg")))
    train_labels = len(list((dataset_path / "labels" / "train").glob("*.txt")))
    val_labels = len(list((dataset_path / "labels" / "val").glob("*.txt")))
    
    print(f"\n数据集验证结果:")
    print(f"训练集图片: {train_images}")
    print(f"训练集标签: {train_labels}")
    print(f"验证集图片: {val_images}")
    print(f"验证集标签: {val_labels}")
    print(f"总计图片: {train_images + val_images}")
    print(f"总计标签: {train_labels + val_labels}")
    
    # 检查图片和标签数量是否匹配
    if train_images != train_labels:
        print(f"警告: 训练集图片和标签数量不匹配")
    if val_images != val_labels:
        print(f"警告: 验证集图片和标签数量不匹配")
    
    return True

def main():
    """主函数"""
    print("=== YOLO数据集处理工具 ===")
    
    # 设置路径
    source_dir = "output-dataset"
    target_dir = "pig_dataset"
    
    # 检查源目录是否存在
    if not Path(source_dir).exists():
        print(f"错误: 源数据集目录不存在: {source_dir}")
        return
    
    # 如果目标目录已存在，询问是否覆盖
    if Path(target_dir).exists():
        response = input(f"目标目录 {target_dir} 已存在，是否删除并重新创建? (y/n): ")
        if response.lower() == 'y':
            shutil.rmtree(target_dir)
            print(f"已删除目录: {target_dir}")
        else:
            print("操作取消")
            return
    
    # 设置随机种子以确保可重现的分割
    random.seed(42)
    
    # 分割数据集
    print(f"\n开始处理数据集...")
    print(f"源目录: {source_dir}")
    print(f"目标目录: {target_dir}")
    print(f"分割比例: 80% 训练集, 20% 验证集")
    
    success = split_dataset(source_dir, target_dir, train_ratio=0.8)
    
    if success:
        print(f"\n数据集处理完成!")
        
        # 验证数据集
        verify_dataset(target_dir)
        
        print(f"\n数据集已准备就绪，可以使用以下命令开始训练:")
        print(f"python train.py")
        
    else:
        print("数据集处理失败!")

if __name__ == "__main__":
    main()
