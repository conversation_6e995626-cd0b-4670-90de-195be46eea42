#!/usr/bin/env python3
"""
简单的运行脚本，用于快速启动标注可视化
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from visualize_annotations import AnnotationVisualizer

def main():
    print("=== YOLO标注可视化工具 ===")
    print()
    
    # 数据集路径（相对于项目根目录）
    dataset_path = "../output-dataset"
    output_path = "visualized_results"
    
    # 检查数据集是否存在
    if not Path(dataset_path).exists():
        print(f"错误: 数据集路径不存在: {dataset_path}")
        print("请确保output-dataset文件夹在项目根目录下")
        return
    
    # 创建可视化器
    print(f"数据集路径: {dataset_path}")
    print(f"输出路径: {output_path}")
    print()
    
    visualizer = AnnotationVisualizer(dataset_path, output_path)
    
    # 创建数据集摘要报告
    print("1. 生成数据集摘要报告...")
    visualizer.create_summary_report()
    print()
    
    # 用户选择
    while True:
        print("请选择操作:")
        print("1. 随机可视化10个样本")
        print("2. 随机可视化指定数量的样本")
        print("3. 可视化指定图片")
        print("4. 退出")
        
        choice = input("请输入选择 (1-4): ").strip()
        
        if choice == "1":
            print("正在随机可视化10个样本...")
            visualizer.visualize_random_samples(10, save_results=True)
            print("完成！")
            
        elif choice == "2":
            try:
                num = int(input("请输入要可视化的样本数量: "))
                print(f"正在随机可视化{num}个样本...")
                visualizer.visualize_random_samples(num, save_results=True)
                print("完成！")
            except ValueError:
                print("请输入有效的数字")
                
        elif choice == "3":
            image_name = input("请输入图片名称（不含扩展名，如output_001）: ").strip()
            if image_name:
                success = visualizer.visualize_single_image(image_name, save_result=True)
                if success:
                    print("完成！")
                else:
                    print("处理失败，请检查图片名称是否正确")
            else:
                print("图片名称不能为空")
                
        elif choice == "4":
            print("退出程序")
            break
            
        else:
            print("无效选择，请重新输入")
        
        print()

if __name__ == "__main__":
    main()
